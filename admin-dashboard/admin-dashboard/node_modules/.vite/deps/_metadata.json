{"hash": "a0dcf98c", "configHash": "60af514e", "lockfileHash": "418b8ca8", "browserHash": "5b25dedd", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d9a40f21", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "c0b8c10a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "4551cf7d", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "2ee2da48", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "6dafc549", "needsInterop": true}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "bf35e63b", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "749adcdd", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "cfdc12b0", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "e5ba963f", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "eb373714", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "b748b045", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "2c965ff5", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "afae3042", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "befd7031", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "88ab37cf", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "19de92b7", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "214d3835", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "190448a3", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "b0fa1201", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "36311bd3", "needsInterop": false}}, "chunks": {"chunk-T63I275V": {"file": "chunk-T63I275V.js"}, "chunk-Y6VO25II": {"file": "chunk-Y6VO25II.js"}, "chunk-S3E2IALR": {"file": "chunk-S3E2IALR.js"}, "chunk-AANGHROB": {"file": "chunk-AANGHROB.js"}, "chunk-FIDP2UAX": {"file": "chunk-FIDP2UAX.js"}, "chunk-E6VPHF2F": {"file": "chunk-E6VPHF2F.js"}, "chunk-5HDB4YN4": {"file": "chunk-5HDB4YN4.js"}, "chunk-Y3AS4J52": {"file": "chunk-Y3AS4J52.js"}, "chunk-S4KYAKJN": {"file": "chunk-S4KYAKJN.js"}, "chunk-JCAXMIFZ": {"file": "chunk-JCAXMIFZ.js"}, "chunk-4T7AQD6K": {"file": "chunk-4T7AQD6K.js"}, "chunk-X6WK577Y": {"file": "chunk-X6WK577Y.js"}, "chunk-P622ID5U": {"file": "chunk-P622ID5U.js"}, "chunk-24NIFAOP": {"file": "chunk-24NIFAOP.js"}, "chunk-3RAKHXQY": {"file": "chunk-3RAKHXQY.js"}, "chunk-PJEEZAML": {"file": "chunk-PJEEZAML.js"}, "chunk-NIL5C5IL": {"file": "chunk-NIL5C5IL.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}
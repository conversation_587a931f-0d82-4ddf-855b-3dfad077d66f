{"version": 3, "sources": ["../../@radix-ui/react-use-callback-ref/src/useCallbackRef.tsx", "../../@radix-ui/react-use-layout-effect/src/useLayoutEffect.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n", "import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = Boolean(globalThis?.document) ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "mappings": ";;;;;;;;AAAA,YAAuB;AAMvB,SAAS,eAAkD,UAA4B;AACrF,QAAM,cAAoB,aAAO,QAAQ;AAEnC,EAAA,gBAAU,MAAM;AACpB,gBAAY,UAAU;EACxB,CAAC;AAGD,SAAa,cAAQ,MAAO,IAAI,SAAA;;AAAS,6BAAY,YAAZ,qCAAsB,GAAG;KAAa,CAAC,CAAC;AACnF;;;ACfA,IAAAA,SAAuB;AASvB,IAAMC,mBAAkB,QAAQ,yCAAY,QAAQ,IAAU,yBAAkB,MAAM;AAAC;", "names": ["React", "useLayoutEffect"]}